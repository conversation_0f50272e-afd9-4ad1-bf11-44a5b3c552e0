@echo off
setlocal enabledelayedexpansion

REM Flink CDC 作业部署脚本 (Windows版本)

REM 配置变量
set FLINK_HOME=%FLINK_HOME%
if "%FLINK_HOME%"=="" set FLINK_HOME=C:\flink
set JOB_NAME=KingbaseES-CDC-Sync
set JAR_FILE=target\flink-kingbase-cdc-1.0.0.jar
set MAIN_CLASS=com.example.KingbaseESCDCJob

REM Flink集群配置
set FLINK_JOBMANAGER_HOST=%FLINK_JOBMANAGER_HOST%
if "%FLINK_JOBMANAGER_HOST%"=="" set FLINK_JOBMANAGER_HOST=localhost
set FLINK_JOBMANAGER_PORT=%FLINK_JOBMANAGER_PORT%
if "%FLINK_JOBMANAGER_PORT%"=="" set FLINK_JOBMANAGER_PORT=8081

echo [INFO] 开始部署KingbaseES CDC作业...

REM 检查Flink环境
echo [INFO] 检查Flink环境...
if not exist "%FLINK_HOME%" (
    echo [ERROR] FLINK_HOME 目录不存在: %FLINK_HOME%
    exit /b 1
)

if not exist "%FLINK_HOME%\bin\flink.bat" (
    echo [ERROR] Flink可执行文件不存在: %FLINK_HOME%\bin\flink.bat
    exit /b 1
)
echo [INFO] Flink环境检查通过

REM 构建项目
echo [INFO] 构建项目...
if not exist "pom.xml" (
    echo [ERROR] pom.xml文件不存在，请确保在项目根目录执行此脚本
    exit /b 1
)

call mvn clean package -DskipTests
if errorlevel 1 (
    echo [ERROR] 项目构建失败
    exit /b 1
)

if not exist "%JAR_FILE%" (
    echo [ERROR] JAR文件不存在: %JAR_FILE%
    exit /b 1
)
echo [INFO] 项目构建成功

REM 检查Flink集群状态
echo [INFO] 检查Flink集群状态...
curl -s "http://%FLINK_JOBMANAGER_HOST%:%FLINK_JOBMANAGER_PORT%/overview" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] 无法连接到Flink集群: http://%FLINK_JOBMANAGER_HOST%:%FLINK_JOBMANAGER_PORT%
    echo [ERROR] 请确保Flink集群正在运行
    exit /b 1
)
echo [INFO] Flink集群状态正常

REM 提交作业
echo [INFO] 提交Flink作业...
call "%FLINK_HOME%\bin\flink.bat" run ^
    --jobmanager %FLINK_JOBMANAGER_HOST%:%FLINK_JOBMANAGER_PORT% ^
    --class %MAIN_CLASS% ^
    --detached ^
    %JAR_FILE%

if errorlevel 1 (
    echo [ERROR] 作业提交失败
    exit /b 1
)

echo [INFO] 作业提交成功
echo [INFO] 可以通过以下URL查看作业状态:
echo [INFO] http://%FLINK_JOBMANAGER_HOST%:%FLINK_JOBMANAGER_PORT%/#/overview

REM 等待作业启动
echo [INFO] 等待作业启动...
timeout /t 10 /nobreak >nul

echo [INFO] 部署完成!

pause
