package com.example;

import java.sql.*;
import java.util.Random;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 测试数据生成器
 * 
 * 用于向源表插入测试数据，以验证CDC同步功能
 */
public class TestDataGenerator {
    
    private static final Logger LOG = LoggerFactory.getLogger(TestDataGenerator.class);
    
    private final ConfigManager configManager;
    private final Random random = new Random();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    private final String[] names = {
        "张三", "李四", "王五", "赵六", "钱七", "孙八", "周九", "吴十",
        "郑十一", "王十二", "冯十三", "陈十四", "褚十五", "卫十六"
    };
    
    private final String[] domains = {
        "@example.com", "@test.com", "@demo.com", "@sample.com"
    };
    
    public TestDataGenerator() {
        this.configManager = new ConfigManager();
    }
    
    public static void main(String[] args) {
        TestDataGenerator generator = new TestDataGenerator();
        
        if (args.length > 0) {
            String command = args[0];
            switch (command) {
                case "insert":
                    int count = args.length > 1 ? Integer.parseInt(args[1]) : 10;
                    generator.insertTestData(count);
                    break;
                case "update":
                    generator.updateRandomData();
                    break;
                case "delete":
                    generator.deleteRandomData();
                    break;
                case "continuous":
                    int intervalSeconds = args.length > 1 ? Integer.parseInt(args[1]) : 5;
                    generator.startContinuousDataGeneration(intervalSeconds);
                    break;
                default:
                    generator.printUsage();
            }
        } else {
            generator.printUsage();
        }
    }
    
    /**
     * 插入测试数据
     */
    public void insertTestData(int count) {
        String sql = "INSERT INTO " + configManager.getSourceTableName() + 
                    " (name, age, email) VALUES (?, ?, ?)";
        
        try (Connection conn = getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            for (int i = 0; i < count; i++) {
                String name = names[random.nextInt(names.length)];
                int age = 20 + random.nextInt(40); // 20-59岁
                String email = generateEmail(name);
                
                stmt.setString(1, name);
                stmt.setInt(2, age);
                stmt.setString(3, email);
                
                stmt.executeUpdate();
                
                LOG.info("插入数据: name={}, age={}, email={}", name, age, email);
            }
            
            LOG.info("成功插入 {} 条测试数据", count);
            
        } catch (SQLException e) {
            LOG.error("插入测试数据失败", e);
        }
    }
    
    /**
     * 更新随机数据
     */
    public void updateRandomData() {
        // 首先获取一个随机的ID
        String selectSql = "SELECT id FROM " + configManager.getSourceTableName() + 
                          " ORDER BY RANDOM() LIMIT 1";
        String updateSql = "UPDATE " + configManager.getSourceTableName() + 
                          " SET name = ?, age = ?, email = ? WHERE id = ?";
        
        try (Connection conn = getConnection();
             PreparedStatement selectStmt = conn.prepareStatement(selectSql);
             PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
            
            ResultSet rs = selectStmt.executeQuery();
            if (rs.next()) {
                long id = rs.getLong("id");
                
                String newName = names[random.nextInt(names.length)];
                int newAge = 20 + random.nextInt(40);
                String newEmail = generateEmail(newName);
                
                updateStmt.setString(1, newName);
                updateStmt.setInt(2, newAge);
                updateStmt.setString(3, newEmail);
                updateStmt.setLong(4, id);
                
                int updated = updateStmt.executeUpdate();
                if (updated > 0) {
                    LOG.info("更新数据: id={}, name={}, age={}, email={}", id, newName, newAge, newEmail);
                }
            } else {
                LOG.warn("没有找到可更新的数据");
            }
            
        } catch (SQLException e) {
            LOG.error("更新数据失败", e);
        }
    }
    
    /**
     * 删除随机数据
     */
    public void deleteRandomData() {
        String selectSql = "SELECT id, name FROM " + configManager.getSourceTableName() + 
                          " ORDER BY RANDOM() LIMIT 1";
        String deleteSql = "DELETE FROM " + configManager.getSourceTableName() + " WHERE id = ?";
        
        try (Connection conn = getConnection();
             PreparedStatement selectStmt = conn.prepareStatement(selectSql);
             PreparedStatement deleteStmt = conn.prepareStatement(deleteSql)) {
            
            ResultSet rs = selectStmt.executeQuery();
            if (rs.next()) {
                long id = rs.getLong("id");
                String name = rs.getString("name");
                
                deleteStmt.setLong(1, id);
                
                int deleted = deleteStmt.executeUpdate();
                if (deleted > 0) {
                    LOG.info("删除数据: id={}, name={}", id, name);
                }
            } else {
                LOG.warn("没有找到可删除的数据");
            }
            
        } catch (SQLException e) {
            LOG.error("删除数据失败", e);
        }
    }
    
    /**
     * 开始连续数据生成
     */
    public void startContinuousDataGeneration(int intervalSeconds) {
        LOG.info("开始连续数据生成，间隔: {} 秒", intervalSeconds);
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                int operation = random.nextInt(10);
                if (operation < 6) {
                    // 60% 概率插入数据
                    insertTestData(1);
                } else if (operation < 9) {
                    // 30% 概率更新数据
                    updateRandomData();
                } else {
                    // 10% 概率删除数据
                    deleteRandomData();
                }
            } catch (Exception e) {
                LOG.error("连续数据生成出错", e);
            }
        }, 0, intervalSeconds, TimeUnit.SECONDS);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            LOG.info("停止数据生成器...");
            scheduler.shutdown();
        }));
        
        // 保持程序运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            LOG.info("程序被中断");
        }
    }
    
    /**
     * 生成邮箱地址
     */
    private String generateEmail(String name) {
        String domain = domains[random.nextInt(domains.length)];
        return name.toLowerCase().replaceAll("\\s+", "") + 
               random.nextInt(1000) + domain;
    }
    
    /**
     * 获取数据库连接
     */
    private Connection getConnection() throws SQLException {
        String url = configManager.getSourceJdbcUrl();
        String username = configManager.getString("source.username", "system");
        String password = configManager.getString("source.password", "123456");
        
        return DriverManager.getConnection(url, username, password);
    }
    
    /**
     * 打印使用说明
     */
    private void printUsage() {
        System.out.println("测试数据生成器使用说明:");
        System.out.println("java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator <command> [args]");
        System.out.println();
        System.out.println("命令:");
        System.out.println("  insert <count>     - 插入指定数量的测试数据 (默认: 10)");
        System.out.println("  update             - 随机更新一条数据");
        System.out.println("  delete             - 随机删除一条数据");
        System.out.println("  continuous <interval> - 连续生成数据，指定间隔秒数 (默认: 5)");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 20");
        System.out.println("  java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator continuous 3");
    }
}
