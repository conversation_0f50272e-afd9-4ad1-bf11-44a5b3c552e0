# KingbaseES CDC 数据同步项目

这个项目使用Apache Flink CDC来实现KingbaseES数据库表之间的实时数据同步。

## 项目结构

```
flink-kingbase-cdc/
├── pom.xml                                    # Maven配置文件
├── src/
│   └── main/
│       ├── java/com/example/
│       │   ├── KingbaseESCDCJob.java         # 主作业类
│       │   └── CDCDataProcessor.java         # CDC数据处理器
│       └── resources/
│           ├── application.properties         # 应用配置
│           └── log4j2.xml                    # 日志配置
├── scripts/
│   ├── init_database.sql                     # 数据库初始化脚本
│   ├── deploy.sh                             # Linux/Mac部署脚本
│   └── deploy.bat                            # Windows部署脚本
└── README.md                                 # 项目说明文档
```

## 功能特性

- **实时数据同步**: 使用Flink CDC实现源表到目标表的实时数据同步
- **支持多种操作**: 支持INSERT、UPDATE、DELETE操作的同步
- **容错机制**: 内置重试机制和错误处理
- **灵活配置**: 通过配置文件轻松修改数据库连接和同步参数
- **监控支持**: 集成日志记录和Flink Web UI监控

## 环境要求

- Java 8+
- Apache Flink 1.17.1+
- KingbaseES 8.6.0+
- Maven 3.6+

## 快速开始

### 1. 环境准备

确保已安装并配置好以下环境：
- Java 8或更高版本
- Apache Flink集群
- KingbaseES数据库
- Maven构建工具

### 2. 数据库配置

在KingbaseES数据库中执行初始化脚本：

```sql
-- 执行数据库初始化脚本
\i scripts/init_database.sql
```

### 3. 配置修改

编辑 `src/main/resources/application.properties` 文件，修改数据库连接信息：

```properties
# 源数据库配置
source.hostname=your-kingbase-host
source.port=54321
source.database=your-database
source.username=your-username
source.password=your-password

# 目标数据库配置
sink.hostname=your-kingbase-host
sink.port=54321
sink.database=your-database
sink.username=your-username
sink.password=your-password
```

### 4. 构建项目

```bash
mvn clean package -DskipTests
```

### 5. 部署作业

#### Linux/Mac系统：
```bash
chmod +x scripts/deploy.sh
./scripts/deploy.sh deploy
```

#### Windows系统：
```cmd
scripts\deploy.bat
```

#### 手动部署：
```bash
$FLINK_HOME/bin/flink run \
    --jobmanager localhost:8081 \
    --class com.example.KingbaseESCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

## 配置说明

### 数据库配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| source.hostname | 源数据库主机 | localhost |
| source.port | 源数据库端口 | 54321 |
| source.database | 源数据库名 | test |
| source.username | 源数据库用户名 | system |
| source.password | 源数据库密码 | 123456 |
| source.schema | 源数据库模式 | public |
| source.table | 源表名 | source_table |

### Flink作业配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| flink.checkpoint.interval | 检查点间隔(ms) | 5000 |
| flink.parallelism | 并行度 | 1 |
| jdbc.batch.size | JDBC批处理大小 | 1000 |
| jdbc.batch.interval | JDBC批处理间隔(ms) | 200 |
| jdbc.max.retries | 最大重试次数 | 5 |

## 监控和运维

### 查看作业状态

访问Flink Web UI：`http://localhost:8081`

### 查看日志

日志文件位置：`logs/flink-cdc.log`

### 停止作业

```bash
# Linux/Mac
./scripts/deploy.sh stop

# 或者通过Flink CLI
$FLINK_HOME/bin/flink cancel <job-id>
```

## 数据表结构

### 源表 (source_table)
```sql
CREATE TABLE public.source_table (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INTEGER,
    email VARCHAR(200),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 目标表 (target_table)
```sql
CREATE TABLE public.target_table (
    id BIGINT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    age INTEGER,
    email VARCHAR(200),
    created_time TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 故障排除

### 常见问题

1. **连接数据库失败**
   - 检查数据库连接配置
   - 确认数据库服务正在运行
   - 验证用户名和密码

2. **CDC无法启动**
   - 确认数据库已启用逻辑复制
   - 检查用户权限
   - 验证表的REPLICA IDENTITY设置

3. **作业提交失败**
   - 检查Flink集群状态
   - 验证JAR文件是否存在
   - 查看Flink JobManager日志

### 日志级别调整

修改 `src/main/resources/log4j2.xml` 中的日志级别：

```xml
<Logger name="com.example" level="DEBUG" additivity="false">
    <AppenderRef ref="Console"/>
    <AppenderRef ref="RollingFileAppender"/>
</Logger>
```

## 扩展功能

### 自定义数据处理

可以修改 `CDCDataProcessor.java` 来实现自定义的数据转换逻辑：

```java
@Override
public Row map(String value) throws Exception {
    // 自定义数据处理逻辑
    // ...
}
```

### 添加更多字段

1. 修改数据库表结构
2. 更新 `CDCDataProcessor.java` 中的字段处理逻辑
3. 调整 `KingbaseESCDCJob.java` 中的SQL语句

## 许可证

本项目采用Apache 2.0许可证。
