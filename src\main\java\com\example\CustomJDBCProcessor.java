package com.example;

import org.apache.flink.api.common.functions.RichMapFunction;
import org.apache.flink.configuration.Configuration;
import org.apache.flink.types.Row;
import org.apache.flink.types.RowKind;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 自定义JDBC处理器
 * 
 * 处理CDC变更数据并直接写入目标数据库
 */
public class CustomJDBCProcessor extends RichMapFunction<Row, String> {
    
    private static final Logger LOG = LoggerFactory.getLogger(CustomJDBCProcessor.class);
    
    private final ConfigManager configManager;
    private transient Connection connection;
    private transient PreparedStatement insertStmt;
    private transient PreparedStatement updateStmt;
    private transient PreparedStatement deleteStmt;
    
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private transient ScheduledExecutorService scheduler;
    
    public CustomJDBCProcessor(ConfigManager configManager) {
        this.configManager = configManager;
    }
    
    @Override
    public void open(Configuration parameters) throws Exception {
        super.open(parameters);
        
        // 初始化数据库连接
        initializeConnection();
        
        // 准备SQL语句
        prepareStatements();
        
        // 启动统计定时器
        scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.scheduleAtFixedRate(() -> {
            LOG.info("已处理CDC记录数: {}", processedCount.get());
        }, 10, 10, TimeUnit.SECONDS);
        
        LOG.info("CustomJDBCProcessor 初始化完成");
    }
    
    @Override
    public void close() throws Exception {
        super.close();
        
        if (scheduler != null) {
            scheduler.shutdown();
        }
        
        // 关闭PreparedStatement
        closeStatement(insertStmt);
        closeStatement(updateStmt);
        closeStatement(deleteStmt);
        
        // 关闭数据库连接
        if (connection != null && !connection.isClosed()) {
            connection.close();
            LOG.info("数据库连接已关闭");
        }
    }
    
    @Override
    public String map(Row row) throws Exception {
        try {
            RowKind rowKind = row.getKind();
            
            switch (rowKind) {
                case INSERT:
                    handleInsert(row);
                    break;
                case UPDATE_AFTER:
                    handleUpdate(row);
                    break;
                case DELETE:
                    handleDelete(row);
                    break;
                case UPDATE_BEFORE:
                    // UPDATE_BEFORE 通常与 UPDATE_AFTER 成对出现，这里忽略
                    break;
                default:
                    LOG.warn("未知的RowKind: {}", rowKind);
            }
            
            processedCount.incrementAndGet();
            
            return String.format("Processed %s: id=%s", rowKind, row.getField(0));
            
        } catch (Exception e) {
            LOG.error("处理CDC记录失败: {}", row, e);
            throw e;
        }
    }
    
    /**
     * 处理插入操作
     */
    private void handleInsert(Row row) throws SQLException {
        setStatementParameters(insertStmt, row);
        
        int result = insertStmt.executeUpdate();
        if (result > 0) {
            LOG.debug("插入记录成功: id={}", row.getField(0));
        } else {
            LOG.warn("插入记录失败: id={}", row.getField(0));
        }
    }
    
    /**
     * 处理更新操作
     */
    private void handleUpdate(Row row) throws SQLException {
        setStatementParameters(updateStmt, row);
        // 设置WHERE条件中的ID
        updateStmt.setObject(6, row.getField(0));
        
        int result = updateStmt.executeUpdate();
        if (result > 0) {
            LOG.debug("更新记录成功: id={}", row.getField(0));
        } else {
            LOG.warn("更新记录失败，记录可能不存在: id={}", row.getField(0));
            // 如果更新失败，尝试插入
            handleInsert(row);
        }
    }
    
    /**
     * 处理删除操作
     */
    private void handleDelete(Row row) throws SQLException {
        deleteStmt.setObject(1, row.getField(0));
        
        int result = deleteStmt.executeUpdate();
        if (result > 0) {
            LOG.debug("删除记录成功: id={}", row.getField(0));
        } else {
            LOG.warn("删除记录失败，记录可能不存在: id={}", row.getField(0));
        }
    }
    
    /**
     * 初始化数据库连接
     */
    private void initializeConnection() throws SQLException {
        String url = configManager.getSinkJdbcUrl();
        String username = configManager.getString("sink.username", "system");
        String password = configManager.getString("sink.password", "123456");
        
        LOG.info("连接目标数据库: {}", url);
        
        connection = DriverManager.getConnection(url, username, password);
        connection.setAutoCommit(true); // 自动提交每个操作
        
        LOG.info("数据库连接建立成功");
    }
    
    /**
     * 准备SQL语句
     */
    private void prepareStatements() throws SQLException {
        String tableName = configManager.getSinkTableName();
        
        // INSERT语句
        String insertSQL = String.format(
                "INSERT INTO %s (id, name, age, email, created_time) VALUES (?, ?, ?, ?, ?)",
                tableName
        );
        insertStmt = connection.prepareStatement(insertSQL);
        
        // UPDATE语句
        String updateSQL = String.format(
                "UPDATE %s SET name = ?, age = ?, email = ?, created_time = ? WHERE id = ?",
                tableName
        );
        updateStmt = connection.prepareStatement(updateSQL);
        
        // DELETE语句
        String deleteSQL = String.format(
                "DELETE FROM %s WHERE id = ?",
                tableName
        );
        deleteStmt = connection.prepareStatement(deleteSQL);
        
        LOG.info("SQL语句准备完成");
    }
    
    /**
     * 设置PreparedStatement参数
     */
    private void setStatementParameters(PreparedStatement stmt, Row row) throws SQLException {
        // 假设Row的结构是: id, name, age, email, created_time
        stmt.setObject(1, row.getField(0)); // id
        stmt.setObject(2, row.getField(1)); // name
        stmt.setObject(3, row.getField(2)); // age
        stmt.setObject(4, row.getField(3)); // email
        stmt.setObject(5, row.getField(4)); // created_time
    }
    
    /**
     * 安全关闭Statement
     */
    private void closeStatement(PreparedStatement stmt) {
        if (stmt != null) {
            try {
                stmt.close();
            } catch (SQLException e) {
                LOG.warn("关闭PreparedStatement失败", e);
            }
        }
    }
}
