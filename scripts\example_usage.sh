#!/bin/bash

# KingbaseES CDC 使用示例脚本

# 配置变量
JAR_FILE="target/flink-kingbase-cdc-1.0.0.jar"
FLINK_HOME=${FLINK_HOME:-"/opt/flink"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查JAR文件是否存在
check_jar() {
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件不存在: $JAR_FILE"
        log_info "请先运行: mvn clean package"
        exit 1
    fi
}

# 显示菜单
show_menu() {
    echo "=================================="
    echo "KingbaseES CDC 使用示例"
    echo "=================================="
    echo "1. 构建项目"
    echo "2. 初始化数据库"
    echo "3. 插入测试数据"
    echo "4. 启动CDC作业"
    echo "5. 监控同步状态"
    echo "6. 连续生成测试数据"
    echo "7. 比较表数据"
    echo "8. 停止CDC作业"
    echo "9. 查看作业状态"
    echo "0. 退出"
    echo "=================================="
}

# 构建项目
build_project() {
    log_step "构建项目..."
    mvn clean package -DskipTests
    if [ $? -eq 0 ]; then
        log_info "项目构建成功"
    else
        log_error "项目构建失败"
        exit 1
    fi
}

# 初始化数据库
init_database() {
    log_step "初始化数据库..."
    log_info "请手动执行以下SQL脚本:"
    log_info "psql -h localhost -p 54321 -U system -d test -f scripts/init_database.sql"
    log_warn "注意: 请确保数据库连接信息正确"
}

# 插入测试数据
insert_test_data() {
    log_step "插入测试数据..."
    check_jar
    
    read -p "请输入要插入的数据条数 (默认: 10): " count
    count=${count:-10}
    
    java -cp "$JAR_FILE" com.example.TestDataGenerator insert $count
}

# 启动CDC作业
start_cdc_job() {
    log_step "启动CDC作业..."
    check_jar
    
    if [ ! -d "$FLINK_HOME" ]; then
        log_error "FLINK_HOME 不存在: $FLINK_HOME"
        log_info "请设置正确的FLINK_HOME环境变量"
        return 1
    fi
    
    log_info "提交Flink作业..."
    $FLINK_HOME/bin/flink run \
        --class com.example.KingbaseESCDCJob \
        --detached \
        "$JAR_FILE"
    
    if [ $? -eq 0 ]; then
        log_info "CDC作业启动成功"
        log_info "可以通过以下URL查看作业状态:"
        log_info "http://localhost:8081/#/overview"
    else
        log_error "CDC作业启动失败"
    fi
}

# 监控同步状态
monitor_sync() {
    log_step "监控同步状态..."
    check_jar
    
    read -p "请输入监控间隔秒数 (默认: 10): " interval
    interval=${interval:-10}
    
    log_info "开始监控，按 Ctrl+C 停止..."
    java -cp "$JAR_FILE" com.example.SyncMonitor monitor $interval
}

# 连续生成测试数据
continuous_data() {
    log_step "连续生成测试数据..."
    check_jar
    
    read -p "请输入数据生成间隔秒数 (默认: 5): " interval
    interval=${interval:-5}
    
    log_info "开始连续生成数据，按 Ctrl+C 停止..."
    java -cp "$JAR_FILE" com.example.TestDataGenerator continuous $interval
}

# 比较表数据
compare_tables() {
    log_step "比较表数据..."
    check_jar
    
    java -cp "$JAR_FILE" com.example.SyncMonitor compare
}

# 停止CDC作业
stop_cdc_job() {
    log_step "停止CDC作业..."
    
    if [ ! -d "$FLINK_HOME" ]; then
        log_error "FLINK_HOME 不存在: $FLINK_HOME"
        return 1
    fi
    
    # 获取作业列表
    log_info "获取运行中的作业..."
    $FLINK_HOME/bin/flink list
    
    read -p "请输入要停止的作业ID: " job_id
    
    if [ -n "$job_id" ]; then
        $FLINK_HOME/bin/flink cancel $job_id
        log_info "作业停止命令已发送"
    else
        log_warn "未输入作业ID"
    fi
}

# 查看作业状态
show_job_status() {
    log_step "查看作业状态..."
    
    if [ ! -d "$FLINK_HOME" ]; then
        log_error "FLINK_HOME 不存在: $FLINK_HOME"
        return 1
    fi
    
    $FLINK_HOME/bin/flink list
    
    log_info "详细状态请访问: http://localhost:8081/#/overview"
}

# 主循环
main() {
    while true; do
        show_menu
        read -p "请选择操作 (0-9): " choice
        
        case $choice in
            1)
                build_project
                ;;
            2)
                init_database
                ;;
            3)
                insert_test_data
                ;;
            4)
                start_cdc_job
                ;;
            5)
                monitor_sync
                ;;
            6)
                continuous_data
                ;;
            7)
                compare_tables
                ;;
            8)
                stop_cdc_job
                ;;
            9)
                show_job_status
                ;;
            0)
                log_info "退出程序"
                exit 0
                ;;
            *)
                log_warn "无效选择，请重新输入"
                ;;
        esac
        
        echo
        read -p "按回车键继续..."
        clear
    done
}

# 检查参数
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "KingbaseES CDC 使用示例脚本"
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  --help, -h    显示帮助信息"
    echo "  --auto        自动模式（构建项目并启动作业）"
    exit 0
fi

if [ "$1" = "--auto" ]; then
    log_info "自动模式启动..."
    build_project
    log_info "请手动初始化数据库后再启动CDC作业"
    exit 0
fi

# 启动交互式菜单
main
