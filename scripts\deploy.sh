#!/bin/bash

# Flink CDC 作业部署脚本

# 配置变量
FLINK_HOME=${FLINK_HOME:-"/home/<USER>"}
JOB_NAME="KingbaseES-CDC-Sync"
JAR_FILE="target/flink-kingbase-cdc-1.0.0.jar"
MAIN_CLASS="com.example.SimpleKingbaseCDCJob"

# Flink集群配置
FLINK_JOBMANAGER_HOST=${FLINK_JOBMANAGER_HOST:-"localhost"}
FLINK_JOBMANAGER_PORT=${FLINK_JOBMANAGER_PORT:-"8081"}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Flink环境
check_flink_env() {
    log_info "检查Flink环境..."
    
    if [ ! -d "$FLINK_HOME" ]; then
        log_error "FLINK_HOME 目录不存在: $FLINK_HOME"
        exit 1
    fi
    
    if [ ! -f "$FLINK_HOME/bin/flink" ]; then
        log_error "Flink可执行文件不存在: $FLINK_HOME/bin/flink"
        exit 1
    fi
    
    log_info "Flink环境检查通过"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    if [ ! -f "pom.xml" ]; then
        log_error "pom.xml文件不存在，请确保在项目根目录执行此脚本"
        exit 1
    fi
    
    mvn clean package -DskipTests
    
    if [ $? -ne 0 ]; then
        log_error "项目构建失败"
        exit 1
    fi
    
    if [ ! -f "$JAR_FILE" ]; then
        log_error "JAR文件不存在: $JAR_FILE"
        exit 1
    fi
    
    log_info "项目构建成功"
}

# 检查Flink集群状态
check_flink_cluster() {
    log_info "检查Flink集群状态..."
    
    # 检查JobManager是否可访问
    curl -s "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/overview" > /dev/null
    
    if [ $? -ne 0 ]; then
        log_error "无法连接到Flink集群: http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}"
        log_error "请确保Flink集群正在运行"
        exit 1
    fi
    
    log_info "Flink集群状态正常"
}

# 停止现有作业
stop_existing_job() {
    log_info "检查并停止现有作业..."
    
    # 获取作业列表
    JOBS=$(curl -s "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/jobs" | grep -o '"jid":"[^"]*"' | cut -d'"' -f4)
    
    for JOB_ID in $JOBS; do
        JOB_INFO=$(curl -s "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/jobs/${JOB_ID}")
        JOB_NAME_CURRENT=$(echo $JOB_INFO | grep -o '"name":"[^"]*"' | cut -d'"' -f4)
        JOB_STATE=$(echo $JOB_INFO | grep -o '"state":"[^"]*"' | cut -d'"' -f4)
        
        if [[ "$JOB_NAME_CURRENT" == *"KingbaseES"* ]] && [[ "$JOB_STATE" == "RUNNING" ]]; then
            log_warn "发现运行中的相关作业: $JOB_NAME_CURRENT ($JOB_ID)"
            log_info "停止作业: $JOB_ID"
            
            curl -X PATCH "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/jobs/${JOB_ID}?mode=cancel"
            
            # 等待作业停止
            sleep 5
        fi
    done
}

# 提交作业
submit_job() {
    log_info "提交Flink作业..."
    
    # 使用Flink CLI提交作业
    $FLINK_HOME/bin/flink run \
        --jobmanager ${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT} \
        --class ${MAIN_CLASS} \
        --detached \
        ${JAR_FILE}
    
    if [ $? -eq 0 ]; then
        log_info "作业提交成功"
        log_info "可以通过以下URL查看作业状态:"
        log_info "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/#/overview"
    else
        log_error "作业提交失败"
        exit 1
    fi
}

# 显示作业状态
show_job_status() {
    log_info "等待作业启动..."
    sleep 10
    
    log_info "当前作业状态:"
    curl -s "http://${FLINK_JOBMANAGER_HOST}:${FLINK_JOBMANAGER_PORT}/jobs" | \
        python3 -m json.tool 2>/dev/null || \
        echo "无法格式化JSON输出，请手动检查作业状态"
}

# 主函数
main() {
    log_info "开始部署KingbaseES CDC作业..."
    
    check_flink_env
    build_project
    check_flink_cluster
    stop_existing_job
    submit_job
    show_job_status
    
    log_info "部署完成!"
}

# 处理命令行参数
case "$1" in
    "build")
        build_project
        ;;
    "deploy")
        main
        ;;
    "stop")
        stop_existing_job
        ;;
    "status")
        show_job_status
        ;;
    *)
        echo "用法: $0 {build|deploy|stop|status}"
        echo "  build  - 仅构建项目"
        echo "  deploy - 构建并部署作业"
        echo "  stop   - 停止现有作业"
        echo "  status - 显示作业状态"
        exit 1
        ;;
esac
