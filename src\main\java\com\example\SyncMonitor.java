package com.example;

import java.sql.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 同步监控工具
 * 
 * 监控源表和目标表的数据同步状态
 */
public class SyncMonitor {
    
    private static final Logger LOG = LoggerFactory.getLogger(SyncMonitor.class);
    
    private final ConfigManager configManager;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    public SyncMonitor() {
        this.configManager = new ConfigManager();
    }
    
    public static void main(String[] args) {
        SyncMonitor monitor = new SyncMonitor();
        
        if (args.length > 0) {
            String command = args[0];
            switch (command) {
                case "check":
                    monitor.checkSyncStatus();
                    break;
                case "monitor":
                    int intervalSeconds = args.length > 1 ? Integer.parseInt(args[1]) : 10;
                    monitor.startMonitoring(intervalSeconds);
                    break;
                case "compare":
                    monitor.compareTableData();
                    break;
                default:
                    monitor.printUsage();
            }
        } else {
            monitor.printUsage();
        }
    }
    
    /**
     * 检查同步状态
     */
    public void checkSyncStatus() {
        try {
            SyncStatus status = getSyncStatus();
            printSyncStatus(status);
        } catch (SQLException e) {
            LOG.error("检查同步状态失败", e);
        }
    }
    
    /**
     * 开始监控
     */
    public void startMonitoring(int intervalSeconds) {
        LOG.info("开始监控同步状态，间隔: {} 秒", intervalSeconds);
        
        scheduler.scheduleAtFixedRate(() -> {
            try {
                SyncStatus status = getSyncStatus();
                printSyncStatus(status);
                
                if (status.getSourceCount() != status.getTargetCount()) {
                    LOG.warn("数据不同步! 源表: {}, 目标表: {}", 
                            status.getSourceCount(), status.getTargetCount());
                }
                
            } catch (Exception e) {
                LOG.error("监控过程中出错", e);
            }
        }, 0, intervalSeconds, TimeUnit.SECONDS);
        
        // 添加关闭钩子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            LOG.info("停止监控...");
            scheduler.shutdown();
        }));
        
        // 保持程序运行
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            LOG.info("监控被中断");
        }
    }
    
    /**
     * 比较表数据
     */
    public void compareTableData() {
        String sourceSql = "SELECT id, name, age, email, created_time FROM " + 
                          configManager.getSourceTableName() + " ORDER BY id";
        String targetSql = "SELECT id, name, age, email, created_time FROM " + 
                          configManager.getSinkTableName() + " ORDER BY id";
        
        try (Connection sourceConn = getSourceConnection();
             Connection targetConn = getTargetConnection();
             PreparedStatement sourceStmt = sourceConn.prepareStatement(sourceSql);
             PreparedStatement targetStmt = targetConn.prepareStatement(targetSql)) {
            
            ResultSet sourceRs = sourceStmt.executeQuery();
            ResultSet targetRs = targetStmt.executeQuery();
            
            int matchCount = 0;
            int mismatchCount = 0;
            int sourceOnlyCount = 0;
            int targetOnlyCount = 0;
            
            while (sourceRs.next() || targetRs.next()) {
                if (sourceRs.isAfterLast() && !targetRs.isAfterLast()) {
                    // 只有目标表有数据
                    targetOnlyCount++;
                    LOG.info("仅目标表存在: id={}", targetRs.getLong("id"));
                    continue;
                }
                
                if (!sourceRs.isAfterLast() && targetRs.isAfterLast()) {
                    // 只有源表有数据
                    sourceOnlyCount++;
                    LOG.info("仅源表存在: id={}", sourceRs.getLong("id"));
                    continue;
                }
                
                // 比较数据
                long sourceId = sourceRs.getLong("id");
                long targetId = targetRs.getLong("id");
                
                if (sourceId == targetId) {
                    // ID相同，比较其他字段
                    if (compareRowData(sourceRs, targetRs)) {
                        matchCount++;
                    } else {
                        mismatchCount++;
                        LOG.warn("数据不匹配: id={}", sourceId);
                    }
                } else if (sourceId < targetId) {
                    sourceOnlyCount++;
                    LOG.info("仅源表存在: id={}", sourceId);
                    // 目标表指针不移动
                    targetRs.previous();
                } else {
                    targetOnlyCount++;
                    LOG.info("仅目标表存在: id={}", targetId);
                    // 源表指针不移动
                    sourceRs.previous();
                }
            }
            
            LOG.info("数据比较结果:");
            LOG.info("  匹配记录: {}", matchCount);
            LOG.info("  不匹配记录: {}", mismatchCount);
            LOG.info("  仅源表存在: {}", sourceOnlyCount);
            LOG.info("  仅目标表存在: {}", targetOnlyCount);
            
        } catch (SQLException e) {
            LOG.error("比较表数据失败", e);
        }
    }
    
    /**
     * 获取同步状态
     */
    private SyncStatus getSyncStatus() throws SQLException {
        SyncStatus status = new SyncStatus();
        
        // 获取源表统计信息
        try (Connection conn = getSourceConnection()) {
            status.setSourceCount(getTableCount(conn, configManager.getSourceTableName()));
            status.setSourceLastUpdate(getLastUpdateTime(conn, configManager.getSourceTableName()));
        }
        
        // 获取目标表统计信息
        try (Connection conn = getTargetConnection()) {
            status.setTargetCount(getTableCount(conn, configManager.getSinkTableName()));
            status.setTargetLastUpdate(getLastUpdateTime(conn, configManager.getSinkTableName()));
        }
        
        return status;
    }
    
    /**
     * 获取表记录数
     */
    private long getTableCount(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT COUNT(*) FROM " + tableName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
        }
        return 0;
    }
    
    /**
     * 获取最后更新时间
     */
    private Timestamp getLastUpdateTime(Connection conn, String tableName) throws SQLException {
        String sql = "SELECT MAX(created_time) FROM " + tableName;
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getTimestamp(1);
            }
        }
        return null;
    }
    
    /**
     * 比较行数据
     */
    private boolean compareRowData(ResultSet sourceRs, ResultSet targetRs) throws SQLException {
        return sourceRs.getString("name").equals(targetRs.getString("name")) &&
               sourceRs.getInt("age") == targetRs.getInt("age") &&
               sourceRs.getString("email").equals(targetRs.getString("email"));
    }
    
    /**
     * 打印同步状态
     */
    private void printSyncStatus(SyncStatus status) {
        LOG.info("=== 同步状态 ===");
        LOG.info("源表记录数: {}", status.getSourceCount());
        LOG.info("目标表记录数: {}", status.getTargetCount());
        LOG.info("源表最后更新: {}", status.getSourceLastUpdate());
        LOG.info("目标表最后更新: {}", status.getTargetLastUpdate());
        
        if (status.getSourceCount() == status.getTargetCount()) {
            LOG.info("状态: 同步正常 ✓");
        } else {
            LOG.warn("状态: 数据不同步 ✗ (差异: {})", 
                    Math.abs(status.getSourceCount() - status.getTargetCount()));
        }
        LOG.info("===============");
    }
    
    /**
     * 获取源数据库连接
     */
    private Connection getSourceConnection() throws SQLException {
        String url = configManager.getSourceJdbcUrl();
        String username = configManager.getString("source.username", "system");
        String password = configManager.getString("source.password", "123456");
        return DriverManager.getConnection(url, username, password);
    }
    
    /**
     * 获取目标数据库连接
     */
    private Connection getTargetConnection() throws SQLException {
        String url = configManager.getSinkJdbcUrl();
        String username = configManager.getString("sink.username", "system");
        String password = configManager.getString("sink.password", "123456");
        return DriverManager.getConnection(url, username, password);
    }
    
    /**
     * 打印使用说明
     */
    private void printUsage() {
        System.out.println("同步监控工具使用说明:");
        System.out.println("java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor <command> [args]");
        System.out.println();
        System.out.println("命令:");
        System.out.println("  check              - 检查当前同步状态");
        System.out.println("  monitor <interval> - 持续监控同步状态，指定间隔秒数 (默认: 10)");
        System.out.println("  compare            - 详细比较源表和目标表数据");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor check");
        System.out.println("  java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor monitor 5");
    }
    
    /**
     * 同步状态数据类
     */
    private static class SyncStatus {
        private long sourceCount;
        private long targetCount;
        private Timestamp sourceLastUpdate;
        private Timestamp targetLastUpdate;
        
        // Getters and Setters
        public long getSourceCount() { return sourceCount; }
        public void setSourceCount(long sourceCount) { this.sourceCount = sourceCount; }
        
        public long getTargetCount() { return targetCount; }
        public void setTargetCount(long targetCount) { this.targetCount = targetCount; }
        
        public Timestamp getSourceLastUpdate() { return sourceLastUpdate; }
        public void setSourceLastUpdate(Timestamp sourceLastUpdate) { this.sourceLastUpdate = sourceLastUpdate; }
        
        public Timestamp getTargetLastUpdate() { return targetLastUpdate; }
        public void setTargetLastUpdate(Timestamp targetLastUpdate) { this.targetLastUpdate = targetLastUpdate; }
    }
}
