package com.example;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * KingbaseES 轮询同步作业
 * 
 * 使用JDBC轮询方式替代CDC，避免兼容性问题
 */
public class KingbaseESPollingJob {
    
    private static final Logger LOG = LoggerFactory.getLogger(KingbaseESPollingJob.class);
    
    public static void main(String[] args) throws Exception {
        // 创建配置管理器
        ConfigManager configManager = new ConfigManager();
        
        // 验证配置
        if (!configManager.validateConfiguration()) {
            LOG.error("配置验证失败，程序退出");
            System.exit(1);
        }
        
        // 打印配置信息
        configManager.printConfiguration();
        
        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置检查点
        long checkpointInterval = configManager.getLong("flink.checkpoint.interval", 5000);
        env.enableCheckpointing(checkpointInterval);
        
        // 设置并行度
        int parallelism = configManager.getInteger("flink.parallelism", 1);
        env.setParallelism(parallelism);
        
        // 创建Table环境
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        
        // 运行轮询作业
        runPollingJob(tableEnv, configManager);
    }
    
    /**
     * 运行基于轮询的同步作业
     */
    private static void runPollingJob(StreamTableEnvironment tableEnv, ConfigManager configManager) throws Exception {
        
        LOG.info("创建JDBC源表（轮询模式）...");
        
        // 创建JDBC源表 - 使用轮询模式
        String sourceTableDDL = String.format(
                "CREATE TABLE source_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  updated_time TIMESTAMP(3)" +
                ") WITH (" +
                "  'connector' = 'jdbc'," +
                "  'url' = '%s'," +
                "  'table-name' = '%s'," +
                "  'username' = '%s'," +
                "  'password' = '%s'," +
                "  'driver' = 'org.postgresql.Driver'," +
                "  'scan.partition.column' = 'id'," +
                "  'scan.partition.num' = '1'," +
                "  'scan.partition.lower-bound' = '1'," +
                "  'scan.partition.upper-bound' = '1000000'," +
                "  'scan.fetch-size' = '1000'" +
                ")",
                configManager.getSourceJdbcUrl(),
                configManager.getSourceTableName(),
                configManager.getString("source.username", "system"),
                configManager.getString("source.password", "123456")
        );
        
        try {
            tableEnv.executeSql(sourceTableDDL);
            LOG.info("JDBC源表创建成功");
        } catch (Exception e) {
            LOG.error("JDBC源表创建失败，可能是因为Flink集群没有JDBC连接器", e);
            LOG.info("请参考 JDBC_CONNECTOR_FIX.md 安装JDBC连接器");
            
            // 使用print连接器作为替代
            createPrintOnlyJob(tableEnv, configManager);
            return;
        }
        
        // 创建print表用于输出
        String printTableDDL = "CREATE TABLE print_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  updated_time TIMESTAMP(3)" +
                ") WITH (" +
                "  'connector' = 'print'" +
                ")";
        
        tableEnv.executeSql(printTableDDL);
        LOG.info("Print表创建成功");
        
        LOG.info("开始轮询数据同步...");
        LOG.info("数据将输出到控制台，请查看TaskManager日志");
        
        // 执行同步
        tableEnv.executeSql("INSERT INTO print_table SELECT * FROM source_table");
        LOG.info("轮询同步作业启动成功");
    }
    
    /**
     * 创建仅使用print连接器的作业（当JDBC连接器不可用时）
     */
    private static void createPrintOnlyJob(StreamTableEnvironment tableEnv, ConfigManager configManager) throws Exception {
        LOG.info("创建仅输出作业（无JDBC连接器）...");
        
        // 创建一个数据生成器表
        String datagenTableDDL = "CREATE TABLE datagen_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)" +
                ") WITH (" +
                "  'connector' = 'datagen'," +
                "  'rows-per-second' = '1'," +
                "  'fields.id.kind' = 'sequence'," +
                "  'fields.id.start' = '1'," +
                "  'fields.id.end' = '1000'," +
                "  'fields.name.length' = '10'," +
                "  'fields.age.min' = '20'," +
                "  'fields.age.max' = '60'," +
                "  'fields.email.length' = '20'" +
                ")";
        
        tableEnv.executeSql(datagenTableDDL);
        LOG.info("数据生成器表创建成功");
        
        // 创建print表
        String printTableDDL = "CREATE TABLE print_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)" +
                ") WITH (" +
                "  'connector' = 'print'" +
                ")";
        
        tableEnv.executeSql(printTableDDL);
        LOG.info("Print表创建成功");
        
        LOG.info("开始模拟数据生成...");
        LOG.info("这是一个演示作业，每秒生成1条模拟数据");
        
        // 执行数据生成
        tableEnv.executeSql("INSERT INTO print_table SELECT * FROM datagen_table");
        LOG.info("数据生成作业启动成功");
    }
}
