-- KingbaseES/PostgreSQL CDC 设置脚本
-- 此脚本需要以超级用户身份执行

-- 1. 检查并设置数据库参数
-- 检查wal_level设置（需要为logical）
SHOW wal_level;

-- 如果wal_level不是logical，需要修改配置文件并重启数据库
-- 在postgresql.conf或kingbase.conf中设置：
-- wal_level = logical
-- max_wal_senders = 10
-- max_replication_slots = 10

-- 2. 创建复制槽
-- 删除已存在的复制槽（如果存在）
SELECT pg_drop_replication_slot('flink_cdc_slot')
WHERE EXISTS (
    SELECT 1 FROM pg_replication_slots
    WHERE slot_name = 'flink_cdc_slot'
);

-- 创建新的复制槽（使用test_decoding插件）
SELECT pg_create_logical_replication_slot('flink_cdc_slot', 'test_decoding');

-- 3. 检查复制槽状态
SELECT slot_name, plugin, slot_type, database, active, restart_lsn
FROM pg_replication_slots
WHERE slot_name = 'flink_cdc_slot';

-- 4. 为CDC用户授权
-- 确保CDC用户有足够的权限
GRANT USAGE ON SCHEMA public TO system;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO system;
GRANT SELECT ON ALL SEQUENCES IN SCHEMA public TO system;

-- 为新创建的表自动授权
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO system;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON SEQUENCES TO system;

-- 5. 设置表的复制标识
-- 为源表设置REPLICA IDENTITY FULL（如果表已存在）
-- ALTER TABLE public.source_table REPLICA IDENTITY FULL;

-- 6. 创建发布（可选，用于逻辑复制）
-- DROP PUBLICATION IF EXISTS flink_cdc_publication;
-- CREATE PUBLICATION flink_cdc_publication FOR ALL TABLES;

-- 7. 验证设置
-- 检查当前数据库的逻辑复制设置
SELECT name, setting FROM pg_settings
WHERE name IN ('wal_level', 'max_wal_senders', 'max_replication_slots');

-- 检查用户权限
SELECT
    schemaname,
    tablename,
    hasselect,
    hasinsert,
    hasupdate,
    hasdelete
FROM pg_tables t
LEFT JOIN (
    SELECT
        schemaname,
        tablename,
        has_table_privilege('system', schemaname||'.'||tablename, 'SELECT') as hasselect,
        has_table_privilege('system', schemaname||'.'||tablename, 'INSERT') as hasinsert,
        has_table_privilege('system', schemaname||'.'||tablename, 'UPDATE') as hasupdate,
        has_table_privilege('system', schemaname||'.'||tablename, 'DELETE') as hasdelete
    FROM pg_tables
    WHERE schemaname = 'public'
) p USING (schemaname, tablename)
WHERE t.schemaname = 'public';

-- 8. 显示帮助信息
SELECT '=== CDC 设置完成 ===' as status;
SELECT '请确保以下配置正确：' as info;
SELECT '1. wal_level = logical' as config_1;
SELECT '2. max_wal_senders >= 10' as config_2;
SELECT '3. max_replication_slots >= 10' as config_3;
SELECT '4. 重启数据库服务（如果修改了配置）' as config_4;
SELECT '5. 复制槽 flink_cdc_slot 已创建' as config_5;
