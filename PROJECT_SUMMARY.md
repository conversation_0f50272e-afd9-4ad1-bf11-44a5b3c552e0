# KingbaseES CDC 项目总结

## 项目概述

本项目提供了一个完整的Apache Flink CDC解决方案，用于实现KingbaseES数据库表之间的实时数据同步。项目使用PostgreSQL CDC连接器（因为KingbaseES兼容PostgreSQL协议）来捕获数据变更，并通过JDBC连接器将数据同步到目标表。

## 项目结构

```
flink-kingbase-cdc/
├── pom.xml                                    # Maven项目配置
├── README.md                                  # 详细使用文档
├── QUICKSTART.md                              # 快速开始指南
├── PROJECT_SUMMARY.md                         # 项目总结（本文件）
├── src/
│   └── main/
│       ├── java/com/example/
│       │   ├── KingbaseESCDCJob.java         # 主作业类
│       │   ├── CDCDataProcessor.java         # CDC数据处理器
│       │   ├── ConfigManager.java            # 配置管理器
│       │   ├── TestDataGenerator.java        # 测试数据生成器
│       │   └── SyncMonitor.java              # 同步监控工具
│       └── resources/
│           ├── application.properties         # 应用配置文件
│           └── log4j2.xml                    # 日志配置
├── scripts/
│   ├── init_database.sql                     # 数据库初始化脚本
│   ├── deploy.sh                             # Linux/Mac部署脚本
│   ├── deploy.bat                            # Windows部署脚本
│   ├── example_usage.sh                      # Linux/Mac示例脚本
│   └── example_usage.bat                     # Windows示例脚本
└── target/
    └── flink-kingbase-cdc-1.0.0.jar         # 构建生成的JAR文件
```

## 核心功能

### 1. 实时数据同步
- 使用Flink CDC技术实现源表到目标表的实时数据同步
- 支持INSERT、UPDATE、DELETE操作的捕获和同步
- 基于Flink Table API实现，确保API稳定性

### 2. 配置管理
- 灵活的配置文件支持（application.properties）
- 支持数据库连接、作业参数、JDBC配置等
- 配置验证和默认值处理

### 3. 监控和测试工具
- **TestDataGenerator**: 生成测试数据，支持连续数据生成
- **SyncMonitor**: 监控同步状态，比较源表和目标表数据
- 完整的日志记录和错误处理

### 4. 部署支持
- 支持Linux/Mac和Windows平台
- 提供自动化部署脚本
- 交互式使用示例脚本

## 技术栈

- **Apache Flink 1.17.1**: 流处理引擎
- **Flink CDC 2.4.2**: 变更数据捕获
- **PostgreSQL Driver**: 数据库连接（兼容KingbaseES）
- **Maven**: 项目构建和依赖管理
- **Log4j2**: 日志记录
- **Jackson**: JSON处理

## 主要类说明

### KingbaseESCDCJob
主作业类，负责：
- 创建Flink执行环境
- 配置CDC源表和JDBC目标表
- 启动数据同步作业

### ConfigManager
配置管理器，提供：
- 配置文件加载和解析
- 配置验证和默认值处理
- JDBC URL构建等工具方法

### CDCDataProcessor
CDC数据处理器，负责：
- 解析Debezium JSON格式的CDC数据
- 数据类型转换和字段映射
- 错误处理和日志记录

### TestDataGenerator
测试数据生成器，支持：
- 批量插入测试数据
- 随机更新和删除操作
- 连续数据生成模式

### SyncMonitor
同步监控工具，提供：
- 实时同步状态检查
- 源表和目标表数据比较
- 持续监控模式

## 使用方式

### 1. 快速开始
```bash
# 构建项目
mvn clean package -DskipTests

# 初始化数据库
psql -h localhost -p 54321 -U system -d test -f scripts/init_database.sql

# 启动CDC作业
$FLINK_HOME/bin/flink run \
    --class com.example.KingbaseESCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

### 2. 使用交互式脚本
```bash
# Linux/Mac
./scripts/example_usage.sh

# Windows
scripts\example_usage.bat
```

### 3. 测试和监控
```bash
# 插入测试数据
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 10

# 监控同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor monitor 5
```

## 配置说明

### 数据库配置
```properties
# 源数据库
source.hostname=localhost
source.port=54321
source.database=test
source.username=system
source.password=123456

# 目标数据库
sink.hostname=localhost
sink.port=54321
sink.database=test
sink.username=system
sink.password=123456
```

### Flink作业配置
```properties
flink.checkpoint.interval=5000
flink.parallelism=1
jdbc.batch.size=1000
jdbc.batch.interval=200
jdbc.max.retries=5
```

## 部署要求

### 环境要求
- Java 8+
- Apache Flink 1.17.1+
- KingbaseES 8.6.0+ (或PostgreSQL兼容数据库)
- Maven 3.6+ (仅构建时需要)

### 网络要求
- Flink集群能够访问源和目标数据库
- 数据库启用逻辑复制功能
- 防火墙允许相关端口通信

## 注意事项

### 1. 数据库配置
- 确保KingbaseES启用了逻辑复制
- 源表需要设置REPLICA IDENTITY FULL
- 用户需要有足够的权限进行CDC操作

### 2. 性能调优
- 根据数据量调整批处理大小和间隔
- 合理设置检查点间隔
- 监控Flink集群资源使用情况

### 3. 故障处理
- 定期检查作业状态和日志
- 配置适当的重试机制
- 建立监控和告警机制

## 扩展建议

### 1. 功能扩展
- 支持更多数据类型和字段映射
- 添加数据转换和过滤逻辑
- 实现多表同步功能

### 2. 监控增强
- 集成Prometheus和Grafana
- 添加更多监控指标
- 实现告警通知功能

### 3. 部署优化
- 支持Kubernetes部署
- 实现CI/CD流水线
- 添加配置中心集成

## 许可证

本项目采用Apache 2.0许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 查看README.md获取详细文档
- 检查QUICKSTART.md获取快速开始指南
- 查看项目日志文件进行故障排除
