package com.example;

import org.apache.flink.streaming.api.environment.StreamExecutionEnvironment;
import org.apache.flink.table.api.bridge.java.StreamTableEnvironment;
import org.apache.flink.configuration.Configuration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 简化的KingbaseES CDC作业
 * 
 * 专门针对KingbaseES兼容性问题设计的简化版本
 */
public class SimpleKingbaseCDCJob {
    
    private static final Logger LOG = LoggerFactory.getLogger(SimpleKingbaseCDCJob.class);
    
    public static void main(String[] args) throws Exception {
        // 创建配置管理器
        ConfigManager configManager = new ConfigManager();
        
        // 验证配置
        if (!configManager.validateConfiguration()) {
            LOG.error("配置验证失败，程序退出");
            System.exit(1);
        }
        
        // 打印配置信息
        configManager.printConfiguration();
        
        // 创建Flink执行环境
        StreamExecutionEnvironment env = StreamExecutionEnvironment.getExecutionEnvironment();
        
        // 设置检查点
        long checkpointInterval = configManager.getLong("flink.checkpoint.interval", 5000);
        env.enableCheckpointing(checkpointInterval);
        
        // 设置并行度
        int parallelism = configManager.getInteger("flink.parallelism", 1);
        env.setParallelism(parallelism);
        
        // 创建Table环境
        StreamTableEnvironment tableEnv = StreamTableEnvironment.create(env);
        
        // 使用最简单的CDC配置
        runSimpleCDCJob(tableEnv, configManager);
    }
    
    /**
     * 运行简化的CDC作业
     */
    private static void runSimpleCDCJob(StreamTableEnvironment tableEnv, ConfigManager configManager) throws Exception {
        
        LOG.info("创建简化的CDC源表...");
        
        // 创建CDC源表 - 使用最基本的配置
        String sourceTableDDL = String.format(
                "CREATE TABLE source_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)," +
                "  PRIMARY KEY (id) NOT ENFORCED" +
                ") WITH (" +
                "  'connector' = 'postgres-cdc'," +
                "  'hostname' = '%s'," +
                "  'port' = '%s'," +
                "  'username' = '%s'," +
                "  'password' = '%s'," +
                "  'database-name' = '%s'," +
                "  'schema-name' = '%s'," +
                "  'table-name' = '%s'," +
                "  'slot.name' = '%s'," +
                "  'scan.startup.mode' = 'latest-offset'" +
                ")",
                configManager.getString("source.hostname", "localhost"),
                configManager.getString("source.port", "54321"),
                configManager.getString("source.username", "system"),
                configManager.getString("source.password", "123456"),
                configManager.getString("source.database", "test"),
                configManager.getString("source.schema", "public"),
                configManager.getString("source.table", "source_table"),
                configManager.getString("cdc.slot.name", "flink_cdc_slot")
        );
        
        try {
            tableEnv.executeSql(sourceTableDDL);
            LOG.info("CDC源表创建成功");
        } catch (Exception e) {
            LOG.error("CDC源表创建失败", e);
            throw e;
        }
        
        // 创建print表用于输出
        String printTableDDL = "CREATE TABLE print_table (" +
                "  id BIGINT," +
                "  name STRING," +
                "  age INT," +
                "  email STRING," +
                "  created_time TIMESTAMP(3)" +
                ") WITH (" +
                "  'connector' = 'print'" +
                ")";
        
        tableEnv.executeSql(printTableDDL);
        LOG.info("Print表创建成功");
        
        LOG.info("开始CDC数据同步...");
        LOG.info("数据将输出到控制台，请查看TaskManager日志");
        
        // 执行同步
        try {
            tableEnv.executeSql("INSERT INTO print_table SELECT id, name, age, email, created_time FROM source_table");
            LOG.info("CDC同步作业启动成功");
        } catch (Exception e) {
            LOG.error("CDC同步作业启动失败", e);
            
            // 尝试使用更简单的查询
            LOG.info("尝试使用简化查询...");
            try {
                tableEnv.executeSql("INSERT INTO print_table SELECT * FROM source_table");
                LOG.info("简化CDC同步作业启动成功");
            } catch (Exception e2) {
                LOG.error("简化CDC同步作业也失败", e2);
                throw e2;
            }
        }
    }
}
