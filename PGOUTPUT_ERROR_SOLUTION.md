# pgoutput 插件错误解决方案

## 问题描述

错误信息：
```
ERROR: could not access file "pgoutput": No such file or directory
io.debezium.DebeziumException: Creation of replication slot failed
```

这个错误表明KingbaseES数据库中没有`pgoutput`逻辑解码插件。

## 解决方案

### 步骤1：检查KingbaseES支持的逻辑解码插件

连接到KingbaseES数据库并执行检查脚本：

```bash
psql -h ************** -p 54321 -U system -d test -f scripts/check_kingbase_plugins.sql
```

或者手动执行：

```sql
-- 检查可用的逻辑解码插件
SELECT name, default_version, comment 
FROM pg_available_extensions 
WHERE name IN ('test_decoding', 'pgoutput', 'wal2json', 'decoderbufs')
ORDER BY name;
```

### 步骤2：使用修复脚本自动配置

执行我们提供的自动修复脚本：

```bash
psql -h ************** -p 54321 -U system -d test -f scripts/fix_kingbase_cdc.sql
```

这个脚本会：
1. 删除现有的复制槽
2. 检查可用的逻辑解码插件
3. 按优先级尝试创建复制槽：
   - `test_decoding` (最通用)
   - `wal2json` (JSON格式输出)
   - `decoderbufs` (Protocol Buffers格式)
4. 设置表的复制标识
5. 授予必要权限

### 步骤3：根据结果更新配置

根据修复脚本的输出，更新 `src/main/resources/application.properties` 中的配置：

```properties
# 如果使用 test_decoding
cdc.decoding.plugin=test_decoding

# 如果使用 wal2json
cdc.decoding.plugin=wal2json

# 如果使用 decoderbufs
cdc.decoding.plugin=decoderbufs
```

### 步骤4：重新构建和部署

```bash
# 重新构建项目
mvn package -DskipTests

# 重新部署作业
./scripts/deploy.sh deploy
```

## 常见的KingbaseES逻辑解码插件

### 1. test_decoding (推荐)
- **优点**: PostgreSQL标准插件，兼容性最好
- **缺点**: 输出格式相对简单
- **配置**: `cdc.decoding.plugin=test_decoding`

### 2. wal2json
- **优点**: JSON格式输出，易于解析
- **缺点**: 需要单独安装
- **配置**: `cdc.decoding.plugin=wal2json`

### 3. decoderbufs
- **优点**: Protocol Buffers格式，性能好
- **缺点**: 需要单独安装，格式复杂
- **配置**: `cdc.decoding.plugin=decoderbufs`

## 手动创建复制槽

如果自动脚本失败，可以手动创建：

```sql
-- 1. 删除现有复制槽（如果存在）
SELECT pg_drop_replication_slot('flink_cdc_slot') 
WHERE EXISTS (
    SELECT 1 FROM pg_replication_slots 
    WHERE slot_name = 'flink_cdc_slot'
);

-- 2. 使用test_decoding创建复制槽
SELECT pg_create_logical_replication_slot('flink_cdc_slot', 'test_decoding');

-- 3. 验证复制槽创建成功
SELECT slot_name, plugin, slot_type, database, active 
FROM pg_replication_slots 
WHERE slot_name = 'flink_cdc_slot';

-- 4. 设置表复制标识
ALTER TABLE public.source_table REPLICA IDENTITY FULL;
```

## 验证配置

### 1. 检查复制槽状态
```sql
SELECT slot_name, plugin, slot_type, database, active, restart_lsn 
FROM pg_replication_slots 
WHERE slot_name = 'flink_cdc_slot';
```

期望结果：
- `slot_name`: flink_cdc_slot
- `plugin`: test_decoding (或其他可用插件)
- `slot_type`: logical
- `active`: f (未使用时) 或 t (使用中)

### 2. 检查表复制标识
```sql
SELECT schemaname, tablename, 
       CASE WHEN relreplident = 'f' THEN 'FULL'
            WHEN relreplident = 'd' THEN 'DEFAULT'
       END as replica_identity
FROM pg_tables t
JOIN pg_class c ON c.relname = t.tablename
WHERE schemaname = 'public' AND tablename = 'source_table';
```

期望结果：`replica_identity` 应该是 `FULL`

### 3. 测试CDC作业
```bash
# 提交作业
$FLINK_HOME/bin/flink run \
    --class com.example.KingbaseESCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar

# 插入测试数据
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 3

# 查看TaskManager日志
tail -f $FLINK_HOME/log/flink-*-taskmanager-*.log
```

## 如果仍然失败

### 1. 联系KingbaseES技术支持
如果所有标准插件都不可用，可能需要：
- 安装逻辑解码插件
- 启用相关扩展
- 配置数据库参数

### 2. 使用替代方案
考虑使用：
- 基于触发器的CDC方案
- 定时轮询方案
- KingbaseES原生的复制功能

### 3. 检查KingbaseES版本
确认您的KingbaseES版本支持逻辑复制：

```sql
SELECT version();
SHOW wal_level;
```

## 成功标志

当看到以下日志时，说明CDC配置成功：

```
2025-08-03 01:55:00,123 INFO  org.apache.flink.runtime.taskmanager.Task [] - +I[1, 张三, 25, <EMAIL>, 2025-08-03T01:54:58.123]
```

这表示CDC成功捕获了数据变更并输出到控制台。

## 下一步

CDC配置成功后，可以：
1. 安装JDBC连接器实现真正的数据同步
2. 配置更多的监控和告警
3. 优化性能参数
4. 扩展到多表同步

参考文档：
- `JDBC_CONNECTOR_FIX.md` - JDBC连接器安装指南
- `TROUBLESHOOTING.md` - 完整故障排除指南
