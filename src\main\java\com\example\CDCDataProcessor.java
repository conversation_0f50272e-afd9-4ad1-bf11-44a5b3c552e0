package com.example;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.flink.api.common.functions.MapFunction;
import org.apache.flink.types.Row;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * CDC数据处理器
 * 
 * 处理从Debezium接收到的JSON格式的CDC数据，
 * 将其转换为Flink Row对象以便写入目标数据库
 */
public class CDCDataProcessor implements MapFunction<String, Row> {
    
    private static final Logger LOG = LoggerFactory.getLogger(CDCDataProcessor.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    @Override
    public Row map(String value) throws Exception {
        try {
            // 解析JSON
            JsonNode rootNode = objectMapper.readTree(value);
            
            // 获取操作类型
            String operation = rootNode.path("op").asText();
            
            // 根据操作类型处理数据
            JsonNode afterNode = rootNode.path("after");
            JsonNode beforeNode = rootNode.path("before");
            
            Row row = null;
            
            switch (operation) {
                case "c": // CREATE (INSERT)
                case "u": // UPDATE
                    if (!afterNode.isMissingNode()) {
                        row = processRowData(afterNode);
                        LOG.info("Processing {} operation for record: {}", 
                                operation.equals("c") ? "INSERT" : "UPDATE", 
                                afterNode.path("id").asLong());
                    }
                    break;
                    
                case "d": // DELETE
                    if (!beforeNode.isMissingNode()) {
                        // 对于删除操作，可以选择标记删除或者实际删除
                        // 这里我们跳过删除操作，如果需要处理删除，可以在这里添加逻辑
                        LOG.info("Skipping DELETE operation for record: {}", 
                                beforeNode.path("id").asLong());
                        return null;
                    }
                    break;
                    
                case "r": // READ (初始快照)
                    if (!afterNode.isMissingNode()) {
                        row = processRowData(afterNode);
                        LOG.info("Processing SNAPSHOT operation for record: {}", 
                                afterNode.path("id").asLong());
                    }
                    break;
                    
                default:
                    LOG.warn("Unknown operation type: {}", operation);
                    return null;
            }
            
            return row;
            
        } catch (Exception e) {
            LOG.error("Error processing CDC data: {}", value, e);
            throw e;
        }
    }
    
    /**
     * 处理行数据，将JSON节点转换为Row对象
     */
    private Row processRowData(JsonNode dataNode) {
        Row row = new Row(5); // 假设有5个字段：id, name, age, email, created_time
        
        try {
            // 处理ID字段
            if (!dataNode.path("id").isMissingNode()) {
                row.setField(0, dataNode.path("id").asLong());
            }
            
            // 处理name字段
            if (!dataNode.path("name").isMissingNode()) {
                row.setField(1, dataNode.path("name").asText());
            }
            
            // 处理age字段
            if (!dataNode.path("age").isMissingNode()) {
                row.setField(2, dataNode.path("age").asInt());
            }
            
            // 处理email字段
            if (!dataNode.path("email").isMissingNode()) {
                row.setField(3, dataNode.path("email").asText());
            }
            
            // 处理时间戳字段
            if (!dataNode.path("created_time").isMissingNode()) {
                String timestampStr = dataNode.path("created_time").asText();
                Timestamp timestamp = parseTimestamp(timestampStr);
                row.setField(4, timestamp);
            } else {
                // 如果没有时间戳，使用当前时间
                row.setField(4, new Timestamp(System.currentTimeMillis()));
            }
            
        } catch (Exception e) {
            LOG.error("Error processing row data: {}", dataNode.toString(), e);
            throw new RuntimeException("Failed to process row data", e);
        }
        
        return row;
    }
    
    /**
     * 解析时间戳字符串
     */
    private Timestamp parseTimestamp(String timestampStr) {
        try {
            // 尝试解析微秒时间戳
            if (timestampStr.matches("\\d+")) {
                long microseconds = Long.parseLong(timestampStr);
                long milliseconds = microseconds / 1000;
                return new Timestamp(milliseconds);
            }
            
            // 尝试解析ISO格式时间戳
            if (timestampStr.contains("T")) {
                Instant instant = Instant.parse(timestampStr);
                return Timestamp.from(instant);
            }
            
            // 尝试解析标准格式时间戳
            LocalDateTime localDateTime = LocalDateTime.parse(timestampStr, TIMESTAMP_FORMATTER);
            return Timestamp.valueOf(localDateTime);
            
        } catch (Exception e) {
            LOG.warn("Failed to parse timestamp: {}, using current time", timestampStr);
            return new Timestamp(System.currentTimeMillis());
        }
    }
    
    /**
     * 获取字段的安全字符串值
     */
    private String getStringValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.path(fieldName);
        return fieldNode.isMissingNode() || fieldNode.isNull() ? null : fieldNode.asText();
    }
    
    /**
     * 获取字段的安全整数值
     */
    private Integer getIntValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.path(fieldName);
        return fieldNode.isMissingNode() || fieldNode.isNull() ? null : fieldNode.asInt();
    }
    
    /**
     * 获取字段的安全长整数值
     */
    private Long getLongValue(JsonNode node, String fieldName) {
        JsonNode fieldNode = node.path(fieldName);
        return fieldNode.isMissingNode() || fieldNode.isNull() ? null : fieldNode.asLong();
    }
}
