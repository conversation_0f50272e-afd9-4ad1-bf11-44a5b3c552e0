# KingbaseES CDC 配置文件

# 源数据库配置
source.hostname=**************
source.port=54321
source.database=test
source.username=system
source.password=Gobon@888
source.schema=public
source.table=source_table

# 目标数据库配置
sink.hostname=**************
sink.port=54321
sink.database=test
sink.username=system
sink.password=Gobon@888
sink.schema=public
sink.table=target_table

# Flink 作业配置
flink.checkpoint.interval=5000
flink.parallelism=1

# JDBC 配置
jdbc.batch.size=1000
jdbc.batch.interval=200
jdbc.max.retries=5

# CDC 配置
cdc.startup.mode=initial
cdc.server.name=kingbase_cdc_server
cdc.slot.name=flink_cdc_slot
cdc.decoding.plugin=decoderbufs

# 日志配置
logging.level.root=INFO
logging.level.com.example=DEBUG
logging.level.com.ververica.cdc=INFO
