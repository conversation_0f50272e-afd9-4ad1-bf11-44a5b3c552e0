# KingbaseES兼容性问题解决方案

## 问题分析

从错误日志可以看出，问题出现在Debezium解析KingbaseES的WAL消息时：

```
Message from transaction {} has {} data columns but only {} of type info
```

这表明KingbaseES的内部WAL格式与标准PostgreSQL存在差异，导致Debezium无法正确解析消息。

## 解决方案

### 方案1：使用简化的CDC配置（推荐尝试）

我们创建了一个简化版本的CDC作业，使用最基本的配置：

```bash
# 使用简化的CDC作业
$FLINK_HOME/bin/flink run \
    --class com.example.SimpleKingbaseCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

这个版本：
- 移除了复杂的Debezium配置
- 使用`latest-offset`启动模式
- 简化了表结构映射

### 方案2：使用JDBC轮询替代CDC（稳定方案）

如果CDC仍然不工作，可以使用基于JDBC轮询的方案：

```bash
# 使用轮询作业
$FLINK_HOME/bin/flink run \
    --class com.example.KingbaseESPollingJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

这个方案：
- 使用JDBC连接器定期轮询源表
- 避免了CDC的兼容性问题
- 适合对实时性要求不是特别高的场景

### 方案3：使用数据生成器演示（测试方案）

如果JDBC连接器也不可用，可以使用数据生成器演示Flink作业：

```bash
# 这会自动降级到数据生成器模式
$FLINK_HOME/bin/flink run \
    --class com.example.KingbaseESPollingJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

## 重新构建和测试

### 1. 重新构建项目

```bash
mvn clean package -DskipTests
```

### 2. 停止现有作业

```bash
# 查看运行中的作业
$FLINK_HOME/bin/flink list

# 停止作业
$FLINK_HOME/bin/flink cancel <job-id>
```

### 3. 测试不同方案

#### 测试简化CDC：
```bash
$FLINK_HOME/bin/flink run \
    --class com.example.SimpleKingbaseCDCJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar

# 插入测试数据
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.TestDataGenerator insert 3

# 查看日志
tail -f $FLINK_HOME/log/flink-*-taskmanager-*.log
```

#### 测试JDBC轮询：
```bash
$FLINK_HOME/bin/flink run \
    --class com.example.KingbaseESPollingJob \
    --detached \
    target/flink-kingbase-cdc-1.0.0.jar
```

## 长期解决方案

### 1. 联系KingbaseES技术支持

向KingbaseES技术支持咨询：
- KingbaseES是否完全兼容PostgreSQL的逻辑复制
- 是否有专门的CDC连接器或插件
- 推荐的数据同步方案

### 2. 考虑使用KingbaseES原生功能

KingbaseES可能提供自己的数据同步功能：
- 物理复制
- 逻辑复制
- 数据同步工具

### 3. 使用触发器方案

在源表上创建触发器，将变更记录到专门的日志表：

```sql
-- 创建变更日志表
CREATE TABLE change_log (
    id BIGSERIAL PRIMARY KEY,
    table_name VARCHAR(100),
    operation VARCHAR(10),
    old_data JSONB,
    new_data JSONB,
    change_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建触发器函数
CREATE OR REPLACE FUNCTION log_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO change_log (table_name, operation, new_data)
        VALUES (TG_TABLE_NAME, 'INSERT', row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO change_log (table_name, operation, old_data, new_data)
        VALUES (TG_TABLE_NAME, 'UPDATE', row_to_json(OLD), row_to_json(NEW));
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO change_log (table_name, operation, old_data)
        VALUES (TG_TABLE_NAME, 'DELETE', row_to_json(OLD));
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 在源表上创建触发器
CREATE TRIGGER source_table_changes
    AFTER INSERT OR UPDATE OR DELETE ON public.source_table
    FOR EACH ROW EXECUTE FUNCTION log_changes();
```

然后使用Flink轮询变更日志表。

## 性能优化建议

### 对于JDBC轮询方案：

1. **添加时间戳字段**：
   ```sql
   ALTER TABLE source_table ADD COLUMN last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
   
   CREATE TRIGGER update_last_modified
       BEFORE UPDATE ON source_table
       FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
   ```

2. **使用增量查询**：
   ```sql
   SELECT * FROM source_table 
   WHERE last_modified > '上次同步时间'
   ORDER BY last_modified;
   ```

3. **创建索引**：
   ```sql
   CREATE INDEX idx_source_table_last_modified ON source_table(last_modified);
   ```

## 监控和告警

无论使用哪种方案，都应该设置监控：

1. **数据同步延迟监控**
2. **数据一致性检查**
3. **作业健康状态监控**

使用我们提供的监控工具：

```bash
# 监控同步状态
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor monitor 10

# 比较数据一致性
java -cp target/flink-kingbase-cdc-1.0.0.jar com.example.SyncMonitor compare
```

## 总结

1. **优先尝试简化CDC配置**
2. **如果CDC不工作，使用JDBC轮询**
3. **考虑触发器+日志表方案**
4. **联系KingbaseES技术支持获取官方建议**
5. **设置适当的监控和告警**

每种方案都有其适用场景，选择最适合您业务需求的方案。
