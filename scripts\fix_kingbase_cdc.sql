-- KingbaseES CDC 修复脚本

-- 1. 删除现有的复制槽（如果存在）
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_replication_slots WHERE slot_name = 'flink_cdc_slot') THEN
        PERFORM pg_drop_replication_slot('flink_cdc_slot');
        RAISE NOTICE '已删除现有复制槽: flink_cdc_slot';
    END IF;
END $$;

-- 2. 检查可用的逻辑解码插件
SELECT '=== 可用的逻辑解码插件 ===' as info;
SELECT name, default_version, comment 
FROM pg_available_extensions 
WHERE name IN ('test_decoding', 'pgoutput', 'wal2json', 'decoderbufs')
ORDER BY name;

-- 3. 尝试使用test_decoding创建复制槽
DO $$
BEGIN
    BEGIN
        PERFORM pg_create_logical_replication_slot('flink_cdc_slot', 'test_decoding');
        RAISE NOTICE '✅ 使用 test_decoding 创建复制槽成功';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '❌ test_decoding 创建失败: %', SQLERRM;
        
        -- 尝试使用wal2json
        BEGIN
            PERFORM pg_create_logical_replication_slot('flink_cdc_slot', 'wal2json');
            RAISE NOTICE '✅ 使用 wal2json 创建复制槽成功';
        EXCEPTION WHEN OTHERS THEN
            RAISE NOTICE '❌ wal2json 创建失败: %', SQLERRM;
            
            -- 尝试使用decoderbufs
            BEGIN
                PERFORM pg_create_logical_replication_slot('flink_cdc_slot', 'decoderbufs');
                RAISE NOTICE '✅ 使用 decoderbufs 创建复制槽成功';
            EXCEPTION WHEN OTHERS THEN
                RAISE NOTICE '❌ decoderbufs 创建失败: %', SQLERRM;
                RAISE NOTICE '⚠️  所有常见插件都不可用，请检查KingbaseES的逻辑解码插件配置';
            END;
        END;
    END;
END $$;

-- 4. 检查复制槽创建结果
SELECT '=== 复制槽状态 ===' as info;
SELECT slot_name, plugin, slot_type, database, active, restart_lsn 
FROM pg_replication_slots 
WHERE slot_name = 'flink_cdc_slot';

-- 5. 如果复制槽创建成功，设置表的复制标识
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM pg_replication_slots WHERE slot_name = 'flink_cdc_slot') THEN
        -- 设置源表的复制标识
        ALTER TABLE public.source_table REPLICA IDENTITY FULL;
        RAISE NOTICE '✅ 已设置源表复制标识为 FULL';
        
        -- 授予权限
        GRANT USAGE ON SCHEMA public TO system;
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO system;
        RAISE NOTICE '✅ 已授予用户权限';
    ELSE
        RAISE NOTICE '❌ 复制槽创建失败，跳过表设置';
    END IF;
END $$;

-- 6. 显示最终状态和建议
SELECT '=== 最终检查 ===' as info;

-- 检查数据库配置
SELECT 'wal_level: ' || setting as config FROM pg_settings WHERE name = 'wal_level';
SELECT 'max_wal_senders: ' || setting as config FROM pg_settings WHERE name = 'max_wal_senders';
SELECT 'max_replication_slots: ' || setting as config FROM pg_settings WHERE name = 'max_replication_slots';

-- 检查表复制标识
SELECT 'source_table replica_identity: ' || 
       CASE WHEN relreplident = 'f' THEN 'FULL'
            WHEN relreplident = 'd' THEN 'DEFAULT'
            WHEN relreplident = 'n' THEN 'NOTHING'
            WHEN relreplident = 'i' THEN 'INDEX'
       END as table_config
FROM pg_class 
WHERE relname = 'source_table' AND relnamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');

-- 显示使用的插件
SELECT '使用的解码插件: ' || plugin as plugin_info
FROM pg_replication_slots 
WHERE slot_name = 'flink_cdc_slot';

-- 显示建议
SELECT '=== 使用建议 ===' as suggestions;
SELECT CASE 
    WHEN EXISTS (SELECT 1 FROM pg_replication_slots WHERE slot_name = 'flink_cdc_slot' AND plugin = 'test_decoding') 
    THEN '✅ 请在配置文件中设置: cdc.decoding.plugin=test_decoding'
    WHEN EXISTS (SELECT 1 FROM pg_replication_slots WHERE slot_name = 'flink_cdc_slot' AND plugin = 'wal2json') 
    THEN '✅ 请在配置文件中设置: cdc.decoding.plugin=wal2json'
    WHEN EXISTS (SELECT 1 FROM pg_replication_slots WHERE slot_name = 'flink_cdc_slot' AND plugin = 'decoderbufs') 
    THEN '✅ 请在配置文件中设置: cdc.decoding.plugin=decoderbufs'
    ELSE '❌ 需要安装逻辑解码插件或联系KingbaseES技术支持'
END as recommendation;
