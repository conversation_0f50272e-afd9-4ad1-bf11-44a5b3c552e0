@echo off
setlocal enabledelayedexpansion

REM KingbaseES CDC 使用示例脚本 (Windows版本)

REM 配置变量
set JAR_FILE=target\flink-kingbase-cdc-1.0.0.jar
set FLINK_HOME=%FLINK_HOME%
if "%FLINK_HOME%"=="" set FLINK_HOME=C:\flink

:main
cls
echo ==================================
echo KingbaseES CDC 使用示例
echo ==================================
echo 1. 构建项目
echo 2. 初始化数据库
echo 3. 插入测试数据
echo 4. 启动CDC作业
echo 5. 监控同步状态
echo 6. 连续生成测试数据
echo 7. 比较表数据
echo 8. 停止CDC作业
echo 9. 查看作业状态
echo 0. 退出
echo ==================================

set /p choice="请选择操作 (0-9): "

if "%choice%"=="1" goto build_project
if "%choice%"=="2" goto init_database
if "%choice%"=="3" goto insert_test_data
if "%choice%"=="4" goto start_cdc_job
if "%choice%"=="5" goto monitor_sync
if "%choice%"=="6" goto continuous_data
if "%choice%"=="7" goto compare_tables
if "%choice%"=="8" goto stop_cdc_job
if "%choice%"=="9" goto show_job_status
if "%choice%"=="0" goto exit_program

echo [WARN] 无效选择，请重新输入
pause
goto main

:build_project
echo [STEP] 构建项目...
call mvn clean package -DskipTests
if errorlevel 1 (
    echo [ERROR] 项目构建失败
    pause
    goto main
)
echo [INFO] 项目构建成功
pause
goto main

:init_database
echo [STEP] 初始化数据库...
echo [INFO] 请手动执行以下SQL脚本:
echo psql -h localhost -p 54321 -U system -d test -f scripts\init_database.sql
echo [WARN] 注意: 请确保数据库连接信息正确
pause
goto main

:insert_test_data
echo [STEP] 插入测试数据...
call :check_jar
if errorlevel 1 goto main

set /p count="请输入要插入的数据条数 (默认: 10): "
if "%count%"=="" set count=10

java -cp "%JAR_FILE%" com.example.TestDataGenerator insert %count%
pause
goto main

:start_cdc_job
echo [STEP] 启动CDC作业...
call :check_jar
if errorlevel 1 goto main

if not exist "%FLINK_HOME%" (
    echo [ERROR] FLINK_HOME 不存在: %FLINK_HOME%
    echo [INFO] 请设置正确的FLINK_HOME环境变量
    pause
    goto main
)

echo [INFO] 提交Flink作业...
call "%FLINK_HOME%\bin\flink.bat" run ^
    --class com.example.KingbaseESCDCJob ^
    --detached ^
    "%JAR_FILE%"

if errorlevel 1 (
    echo [ERROR] CDC作业启动失败
) else (
    echo [INFO] CDC作业启动成功
    echo [INFO] 可以通过以下URL查看作业状态:
    echo [INFO] http://localhost:8081/#/overview
)
pause
goto main

:monitor_sync
echo [STEP] 监控同步状态...
call :check_jar
if errorlevel 1 goto main

set /p interval="请输入监控间隔秒数 (默认: 10): "
if "%interval%"=="" set interval=10

echo [INFO] 开始监控，按 Ctrl+C 停止...
java -cp "%JAR_FILE%" com.example.SyncMonitor monitor %interval%
pause
goto main

:continuous_data
echo [STEP] 连续生成测试数据...
call :check_jar
if errorlevel 1 goto main

set /p interval="请输入数据生成间隔秒数 (默认: 5): "
if "%interval%"=="" set interval=5

echo [INFO] 开始连续生成数据，按 Ctrl+C 停止...
java -cp "%JAR_FILE%" com.example.TestDataGenerator continuous %interval%
pause
goto main

:compare_tables
echo [STEP] 比较表数据...
call :check_jar
if errorlevel 1 goto main

java -cp "%JAR_FILE%" com.example.SyncMonitor compare
pause
goto main

:stop_cdc_job
echo [STEP] 停止CDC作业...

if not exist "%FLINK_HOME%" (
    echo [ERROR] FLINK_HOME 不存在: %FLINK_HOME%
    pause
    goto main
)

echo [INFO] 获取运行中的作业...
call "%FLINK_HOME%\bin\flink.bat" list

set /p job_id="请输入要停止的作业ID: "

if not "%job_id%"=="" (
    call "%FLINK_HOME%\bin\flink.bat" cancel %job_id%
    echo [INFO] 作业停止命令已发送
) else (
    echo [WARN] 未输入作业ID
)
pause
goto main

:show_job_status
echo [STEP] 查看作业状态...

if not exist "%FLINK_HOME%" (
    echo [ERROR] FLINK_HOME 不存在: %FLINK_HOME%
    pause
    goto main
)

call "%FLINK_HOME%\bin\flink.bat" list
echo [INFO] 详细状态请访问: http://localhost:8081/#/overview
pause
goto main

:check_jar
if not exist "%JAR_FILE%" (
    echo [ERROR] JAR文件不存在: %JAR_FILE%
    echo [INFO] 请先运行: mvn clean package
    pause
    exit /b 1
)
exit /b 0

:exit_program
echo [INFO] 退出程序
exit /b 0
