-- 检查KingbaseES支持的逻辑解码插件

-- 1. 检查可用的逻辑解码插件
SELECT name, default_version, installed_version, comment 
FROM pg_available_extensions 
WHERE name LIKE '%output%' OR name LIKE '%decode%' OR name LIKE '%logical%';

-- 2. 检查已安装的扩展
SELECT extname, extversion, extrelocatable, extnamespace 
FROM pg_extension 
WHERE extname LIKE '%output%' OR extname LIKE '%decode%' OR extname LIKE '%logical%';

-- 3. 检查复制槽支持的插件
SELECT slot_name, plugin, slot_type, database, active 
FROM pg_replication_slots;

-- 4. 尝试查看系统支持的解码插件
SELECT * FROM pg_catalog.pg_proc 
WHERE proname LIKE '%output%' AND prokind = 'f';

-- 5. 检查数据库版本和配置
SELECT version();
SHOW wal_level;
SHOW max_replication_slots;
SHOW max_wal_senders;

-- 6. 检查是否有test_decoding插件（PostgreSQL标准插件）
SELECT name FROM pg_available_extensions WHERE name = 'test_decoding';

-- 7. 显示帮助信息
SELECT '=== 检查结果说明 ===' as info;
SELECT '如果看到 test_decoding 插件，可以使用它替代 pgoutput' as suggestion_1;
SELECT '如果没有找到任何逻辑解码插件，需要安装或启用相关插件' as suggestion_2;
SELECT '请将查询结果发送给开发人员以确定正确的插件名称' as suggestion_3;
